-- Tabla para almacenar las marcas
CREATE TABLE marcas (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  
  -- Información básica
  brand_name TEXT NOT NULL,
  website TEXT,
  industry TEXT NOT NULL,
  
  -- Identidad visual
  logo_url TEXT,
  primary_color TEXT NOT NULL DEFAULT '#3018ef',
  secondary_color TEXT NOT NULL DEFAULT '#dd3a5a',
  
  -- Audiencia y tono
  target_audience TEXT NOT NULL,
  tone TEXT NOT NULL,
  personality TEXT[] DEFAULT '{}',
  
  -- Posicionamiento
  description TEXT NOT NULL,
  unique_value TEXT NOT NULL,
  competitors TEXT,
  
  -- Documentos y ejemplos
  documents TEXT[] DEFAULT '{}',
  examples TEXT,
  
  -- Metadata
  status TEXT NOT NULL DEFAULT 'draft' CHECK (status IN ('draft', 'active', 'archived')),
  campaigns_count INTEGER DEFAULT 0,
  assets_count INTEGER DEFAULT 0,
  
  -- Usuario (opcional para multi-tenant)
  user_id TEXT
);

-- Índices para mejorar performance
CREATE INDEX idx_nucleos_user_id ON nucleos(user_id);
CREATE INDEX idx_nucleos_status ON nucleos(status);
CREATE INDEX idx_nucleos_updated_at ON nucleos(updated_at DESC);
CREATE INDEX idx_nucleos_brand_name ON nucleos(brand_name);

-- Función para actualizar updated_at automáticamente
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger para actualizar updated_at
CREATE TRIGGER update_nucleos_updated_at 
    BEFORE UPDATE ON nucleos 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Habilitar Row Level Security (RLS)
ALTER TABLE nucleos ENABLE ROW LEVEL SECURITY;

-- Política para que los usuarios solo vean sus propios núcleos
CREATE POLICY "Users can view their own nucleos" ON nucleos
    FOR SELECT USING (auth.uid()::text = user_id);

CREATE POLICY "Users can insert their own nucleos" ON nucleos
    FOR INSERT WITH CHECK (auth.uid()::text = user_id);

CREATE POLICY "Users can update their own nucleos" ON nucleos
    FOR UPDATE USING (auth.uid()::text = user_id);

CREATE POLICY "Users can delete their own nucleos" ON nucleos
    FOR DELETE USING (auth.uid()::text = user_id);

-- ===============================================
-- DESIGN TOOLS TABLES
-- ===============================================

-- Tabla para almacenar análisis de complejidad visual
CREATE TABLE design_analyses (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,

  -- Usuario propietario
  user_id TEXT NOT NULL,

  -- Información del archivo analizado
  original_filename TEXT NOT NULL,
  file_size INTEGER,
  file_type TEXT,
  file_url TEXT, -- URL del archivo almacenado

  -- Parámetros de análisis
  tool_type TEXT NOT NULL DEFAULT 'visual_complexity_analyzer',
  analysis_version TEXT DEFAULT '1.0',

  -- Resultados del análisis
  overall_score INTEGER NOT NULL,
  complexity_scores JSONB NOT NULL, -- {color: 5, layout: 7, typography: 6, etc.}
  analysis_areas JSONB NOT NULL, -- Array de áreas analizadas con scores y descripciones
  recommendations JSONB NOT NULL, -- Array de recomendaciones categorizadas

  -- Análisis detallado de IA
  ai_analysis_summary TEXT,
  gemini_analysis TEXT,
  agent_message TEXT,
  visuai_insights JSONB,

  -- Metadata adicional
  analysis_duration_ms INTEGER,
  status TEXT NOT NULL DEFAULT 'completed' CHECK (status IN ('processing', 'completed', 'failed')),
  error_message TEXT,

  -- Favoritos y etiquetas
  is_favorite BOOLEAN DEFAULT FALSE,
  tags TEXT[] DEFAULT '{}',
  notes TEXT
);

-- Tabla para almacenar archivos subidos por usuarios (para contexto)
CREATE TABLE design_uploads (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,

  -- Usuario propietario
  user_id TEXT NOT NULL,

  -- Información del archivo
  original_filename TEXT NOT NULL,
  file_size INTEGER NOT NULL,
  file_type TEXT NOT NULL,
  file_url TEXT NOT NULL, -- URL del archivo en storage
  file_hash TEXT, -- Hash para evitar duplicados

  -- Metadata
  upload_source TEXT DEFAULT 'design_complexity_analyzer',
  is_processed BOOLEAN DEFAULT FALSE,

  -- Relación con análisis
  analysis_id UUID REFERENCES design_analyses(id) ON DELETE SET NULL
);

-- Índices para mejorar performance
CREATE INDEX idx_design_analyses_user_id ON design_analyses(user_id);
CREATE INDEX idx_design_analyses_created_at ON design_analyses(created_at DESC);
CREATE INDEX idx_design_analyses_tool_type ON design_analyses(tool_type);
CREATE INDEX idx_design_analyses_status ON design_analyses(status);
CREATE INDEX idx_design_analyses_is_favorite ON design_analyses(is_favorite);
CREATE INDEX idx_design_analyses_overall_score ON design_analyses(overall_score);

CREATE INDEX idx_design_uploads_user_id ON design_uploads(user_id);
CREATE INDEX idx_design_uploads_file_hash ON design_uploads(file_hash);
CREATE INDEX idx_design_uploads_analysis_id ON design_uploads(analysis_id);

-- Triggers para actualizar updated_at
CREATE TRIGGER update_design_analyses_updated_at
    BEFORE UPDATE ON design_analyses
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Habilitar Row Level Security (RLS)
ALTER TABLE design_analyses ENABLE ROW LEVEL SECURITY;
ALTER TABLE design_uploads ENABLE ROW LEVEL SECURITY;

-- Políticas RLS para design_analyses
CREATE POLICY "Users can view their own design analyses" ON design_analyses
    FOR SELECT USING (auth.uid()::text = user_id);

CREATE POLICY "Users can insert their own design analyses" ON design_analyses
    FOR INSERT WITH CHECK (auth.uid()::text = user_id);

CREATE POLICY "Users can update their own design analyses" ON design_analyses
    FOR UPDATE USING (auth.uid()::text = user_id);

CREATE POLICY "Users can delete their own design analyses" ON design_analyses
    FOR DELETE USING (auth.uid()::text = user_id);

-- Políticas RLS para design_uploads
CREATE POLICY "Users can view their own design uploads" ON design_uploads
    FOR SELECT USING (auth.uid()::text = user_id);

CREATE POLICY "Users can insert their own design uploads" ON design_uploads
    FOR INSERT WITH CHECK (auth.uid()::text = user_id);

CREATE POLICY "Users can update their own design uploads" ON design_uploads
    FOR UPDATE USING (auth.uid()::text = user_id);

CREATE POLICY "Users can delete their own design uploads" ON design_uploads
    FOR DELETE USING (auth.uid()::text = user_id);

-- Insertar algunos datos de ejemplo (opcional)
INSERT INTO marcas (
  brand_name,
  industry,
  target_audience,
  tone,
  description,
  unique_value,
  personality,
  status
) VALUES
(
  'Emma Studio',
  'SaaS & Tecnología',
  'Marketers, agencias de marketing, emprendedores que buscan automatizar y mejorar sus estrategias de marketing con IA',
  'Innovador y Visionario',
  'Plataforma de marketing con IA revolucionaria que permite a las marcas ejecutar campañas inteligentes sin empezar desde cero',
  'Emma aprende la identidad de tu marca una sola vez y ejecuta con ese contexto en todas las herramientas, eliminando la necesidad de repetir briefs',
  ARRAY['Innovadora', 'Confiable', 'Moderna', 'Experta'],
  'active'
),
(
  'TechFlow Solutions',
  'Tecnología',
  'CTOs, desarrolladores, empresas tecnológicas que buscan soluciones escalables',
  'Técnico y Preciso',
  'Soluciones tecnológicas avanzadas para empresas que buscan optimizar sus procesos digitales',
  'Combinamos expertise técnico con innovación para crear soluciones que realmente escalan',
  ARRAY['Técnica', 'Confiable', 'Innovadora', 'Experta'],
  'active'
);
